"use client";

import { useRef } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import clsx from "clsx";
import { AnimatePresence, motion, useIsPresent } from "framer-motion";

import { useIsInsideMobileNavigation } from "@/components/MobileNavigation";
import { useSectionStore } from "@/components/SectionProvider";
import { Tag } from "@/components/Tag";
import { remToPx } from "@/lib/remToPx";
import getClasses from "@/data/get-classes";
import getTypes from "@/data/get-types";
import getInterfaces from "@/data/get-interfaces";
import getFunctions from "@/data/get-functions";
import { pageRoutes } from "@/data/page-routes";
import { useRouteContext } from "@/contexts/route-context";

interface NavGroup {
  title: string;
  links: Array<{
    title: string;
    href: string;
  }>;
}

function useInitialValue<T>(value: T, condition = true) {
  let initialValue = useRef(value).current;
  return condition ? initialValue : value;
}

function TopLevelNavItem({
  href,
  id,
  children,
}: {
  href: string;
  id: string;
  children: React.ReactNode;
}) {
  const { currentRoute, setCurrentRoute } = useRouteContext();

  return (
    <li className="mb-2">
      <Link
        href={href}
        // className={`block py-1 text-lg transition hover:text-zinc-900 dark:hover:text-white font-semibold text-zinc-900 dark:text-white`}

        className={clsx(
          "block py-1 text-lg transition hover:text-zinc-900 dark:hover:text-white font-semibold",
          currentRoute == id
            ? "text-zinc-900 dark:text-white"
            : "text-zinc-600 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-white"
        )}
        onClick={() => {
          setTimeout(() => {
            setCurrentRoute(id);
          }, 500);
        }}
      >
        {children}
      </Link>
    </li>
  );
}

function NavLink({
  href,
  children,
  tag,
  active = false,
  isAnchorLink = false,
}: {
  href: string;
  children: React.ReactNode;
  tag?: string;
  active?: boolean;
  isAnchorLink?: boolean;
}) {
  return (
    <Link
      href={href}
      aria-current={active ? "page" : undefined}
      className={clsx(
        "flex justify-between gap-2 py-1 pr-3 text-sm transition",
        isAnchorLink ? "pl-7" : "pl-4",
        active
          ? "text-zinc-900 dark:text-white"
          : "text-zinc-600 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-white"
      )}
    >
      <span className="truncate">{children}</span>
      {tag && (
        <Tag variant="small" color="zinc">
          {tag}
        </Tag>
      )}
    </Link>
  );
}

function VisibleSectionHighlight({
  group,
  pathname,
}: {
  group: NavGroup;
  pathname: string;
}) {
  let [sections, visibleSections] = useInitialValue(
    [
      useSectionStore((s) => s.sections),
      useSectionStore((s) => s.visibleSections),
    ],
    useIsInsideMobileNavigation()
  );

  let isPresent = useIsPresent();
  let firstVisibleSectionIndex = Math.max(
    0,
    [{ id: "_top" }, ...sections].findIndex(
      (section) => section.id === visibleSections[0]
    )
  );
  let itemHeight = remToPx(2);
  let height = isPresent
    ? Math.max(1, visibleSections.length) * itemHeight
    : itemHeight;
  let top =
    group.links.findIndex((link) => link.href === pathname) * itemHeight +
    firstVisibleSectionIndex * itemHeight;

  return (
    <motion.div
      layout
      initial={{ opacity: 0 }}
      animate={{ opacity: 1, transition: { delay: 0.2 } }}
      exit={{ opacity: 0 }}
      className="absolute inset-x-0 top-0 bg-zinc-800/2.5 will-change-transform dark:bg-white/2.5"
      style={{ borderRadius: 8, height, top }}
    />
  );
}

function ActivePageMarker({
  group,
  pathname,
}: {
  group: NavGroup;
  pathname: string;
}) {
  let itemHeight = remToPx(2);
  let offset = remToPx(0.25);
  let activePageIndex = group.links.findIndex((link) => link.href === pathname);
  let top = offset + activePageIndex * itemHeight;

  return (
    <motion.div
      layout
      className="absolute left-2 h-6 w-px bg-emerald-500"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1, transition: { delay: 0.2 } }}
      exit={{ opacity: 0 }}
      style={{ top }}
    />
  );
}

function NavigationGroup({
  group,
  className,
}: {
  group: NavGroup;
  className?: string;
}) {
  // If this is the mobile navigation then we always render the initial
  // state, so that the state does not change during the close animation.
  // The state will still update when we re-open (re-render) the navigation.
  let isInsideMobileNavigation = useIsInsideMobileNavigation();
  let [pathname, sections] = useInitialValue(
    [usePathname(), useSectionStore((s) => s.sections)],
    isInsideMobileNavigation
  );

  let isActiveGroup =
    group.links.findIndex((link) => link.href === pathname) !== -1;

  return (
    <li className={clsx("relative mt-6", className)}>
      <motion.h2
        layout="position"
        className="text-xs font-semibold text-zinc-900 dark:text-white"
      >
        {group.title}
      </motion.h2>
      <div className="relative mt-3 pl-2">
        <AnimatePresence initial={!isInsideMobileNavigation}>
          {isActiveGroup && (
            <VisibleSectionHighlight group={group} pathname={pathname} />
          )}
        </AnimatePresence>
        <motion.div
          layout
          className="absolute inset-y-0 left-2 w-px bg-zinc-900/10 dark:bg-white/5"
        />
        <AnimatePresence initial={false}>
          {isActiveGroup && (
            <ActivePageMarker group={group} pathname={pathname} />
          )}
        </AnimatePresence>
        <ul role="list" className="border-l border-transparent">
          {group.links.map((link) => (
            <motion.li key={link.href} layout="position" className="relative">
              <NavLink href={link.href} active={link.href === pathname}>
                {link.title}
              </NavLink>

              <AnimatePresence mode="popLayout" initial={false}>
                {link.href === pathname && sections.length > 0 && (
                  <motion.ul
                    role="list"
                    initial={{ opacity: 0 }}
                    animate={{
                      opacity: 1,
                      transition: { delay: 0.1 },
                    }}
                    exit={{
                      opacity: 0,
                      transition: { duration: 0.15 },
                    }}
                  >
                    {sections.map((section) => (
                      <li key={section.id}>
                        <NavLink
                          href={`${link.href}#${section.id}`}
                          tag={section.tag}
                          isAnchorLink
                        >
                          {section.title}
                        </NavLink>
                      </li>
                    ))}
                  </motion.ul>
                )}
              </AnimatePresence>
            </motion.li>
          ))}
        </ul>
      </div>
    </li>
  );
}

// export const navigation: Array<NavGroup> = [
//   // {
//   //   title: 'Guides',
//   //   links: [
//   //     { title: 'Introduction', href: '/' },
//   //     { title: 'Quickstart', href: '/quickstart' },
//   //     { title: 'SDKs', href: '/sdks' },
//   //     { title: 'Authentication', href: '/authentication' },
//   //     { title: 'Pagination', href: '/pagination' },
//   //     { title: 'Errors', href: '/errors' },
//   //     { title: 'Webhooks', href: '/webhooks' },
//   //   ],
//   // },
//   // {
//   //   title: 'Resources',
//   //   links: [
//   //     { title: 'Contacts', href: '/contacts' },
//   //     { title: 'Conversations', href: '/conversations' },
//   //     { title: 'Messages', href: '/messages' },
//   //     { title: 'Groups', href: '/groups' },
//   //     { title: 'Attachments', href: '/attachments' },
//   //   ],
//   // },
//   {
//     title: "Classes",
//     links: getClasses('transaction').map((item: any) => {
//       return { title: item.name, href: `/classes/${item.name}` };
//     }),
//   },
//   {
//     title: "Interfaces",
//     links: getInterfaces('transaction').map((item: any) => {
//       return { title: item.name, href: `/interfaces/${item.name}` };
//     }),
//   },
//   {
//     title: "Types",
//     links: getTypes('transaction').map((item: any) => {
//       return { title: item.name, href: `/types/${item.name}` };
//     }),
//   },
//   {
//     title: "Functions",
//     links: getFunctions('transaction').map((item: any) => {
//       return { title: item.name, href: `/functions/${item.name}` };
//     }),
//   },
// ];

export function Navigation(props: React.ComponentPropsWithoutRef<"nav">) {
  const { currentRoute } = useRouteContext();

  const navigation: Array<NavGroup> = [
    // {
    //   title: 'Guides',
    //   links: [
    //     { title: 'Introduction', href: '/' },
    //     { title: 'Quickstart', href: '/quickstart' },
    //     { title: 'SDKs', href: '/sdks' },
    //     { title: 'Authentication', href: '/authentication' },
    //     { title: 'Pagination', href: '/pagination' },
    //     { title: 'Errors', href: '/errors' },
    //     { title: 'Webhooks', href: '/webhooks' },
    //   ],
    // },
    // {
    //   title: 'Resources',
    //   links: [
    //     { title: 'Contacts', href: '/contacts' },
    //     { title: 'Conversations', href: '/conversations' },
    //     { title: 'Messages', href: '/messages' },
    //     { title: 'Groups', href: '/groups' },
    //     { title: 'Attachments', href: '/attachments' },
    //   ],
    // },
    {
      title: "Classes",
      links: getClasses(currentRoute).map((item: any) => {
        return {
          title: item.name,
          href: `/${currentRoute}/classes/${item.name}`,
        };
      }),
    },
    {
      title: "Interfaces",
      links: getInterfaces(currentRoute).map((item: any) => {
        return {
          title: item.name,
          href: `/${currentRoute}/interfaces/${item.name}`,
        };
      }),
    },
    {
      title: "Types",
      links: getTypes(currentRoute).map((item: any) => {
        return {
          title: item.name,
          href: `/${currentRoute}/types/${item.name}`,
        };
      }),
    },
    {
      title: "Functions",
      links: getFunctions(currentRoute).map((item: any) => {
        return {
          title: item.name,
          href: `/${currentRoute}/functions/${item.name}`,
        };
      }),
    },
  ];

  return (
    <nav {...props}>
      <ul role="list">
        {/* <TopLevelNavItem href="https://meshjs.dev/">meshjs.dev</TopLevelNavItem>
        <TopLevelNavItem href="#">Documentation</TopLevelNavItem>
        <TopLevelNavItem href="#">Support</TopLevelNavItem> */}
        {pageRoutes.map((route) => (
          <TopLevelNavItem key={route.url} href={route.url} id={route.id}>
            {route.title}
          </TopLevelNavItem>
        ))}

        <div className="my-8"></div>

        {navigation.map(
          (group, groupIndex) =>
            group.links.length > 0 && (
              <NavigationGroup
                key={group.title}
                group={group}
                className={groupIndex === 0 ? "md:mt-0" : ""}
              />
            )
        )}
        {/* <li className="sticky bottom-0 z-10 mt-6 min-[416px]:hidden">
          <Button href="#" variant="filled" className="w-full">
            Sign in
          </Button>
        </li> */}
      </ul>
    </nav>
  );
}
