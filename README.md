# Hydra Wallet Nuxt

A modern Nuxt 3 application with Shadcn UI components, Tailwind CSS, TypeScript, and an enhanced Cardano transaction builder inspired by Mesh SDK patterns.

## 🚀 Features

- **Modern UI**: Shadcn UI components with Tailwind CSS
- **Enhanced Transaction Builder**: Mesh SDK-inspired API using CardanoWASM
- **Type Safety**: Full TypeScript support with custom Cardano interfaces
- **Hydra Support**: Built for Cardano Hydra layer 2 scaling
- **Playground**: Interactive component showcase and testing environment

## Project Structure

```
├── assets/              # Static assets like CSS
│   └── css/             # CSS files including Tailwind config
├── components/          # Vue components
│   └── ui/              # Shadcn UI components
├── lib/                 # Utility functions
├── pages/               # Application pages
├── public/              # Public static assets
├── server/              # Server-side code
├── nuxt.config.ts       # Nuxt configuration
├── components.json      # Shadcn UI configuration
└── tailwind.config.js   # Tailwind CSS configuration
```

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

## Adding Shadcn UI Components

This project uses [shadcn-nuxt](https://www.shadcn-vue.com/), a collection of reusable UI components for Nuxt.

### Using the CLI

1. Run the add command:

```bash
npx shadcn-vue@latest add
```

2. Select the component you want to add (e.g., button, card, dialog)

3. The component will be added to your `components/ui` directory

### Manual Installation

1. Find the component you want on the [Shadcn Vue documentation](https://www.shadcn-vue.com/docs/components)

2. Copy the component code to your `components/ui` directory

3. Import and use the component in your pages or other components:

```vue
<script setup lang="ts">
import { Button } from '@/components/ui/button'
</script>

<template>
  <Button>Click me</Button>
</template>
```

### Component Customization

You can customize the appearance of components by:

1. Modifying the component files directly in `components/ui`
2. Adjusting the theme in `assets/css/tailwind.css`
3. Updating the variants in component index files (e.g., `components/ui/button/index.ts`)

## Styling

This project uses Tailwind CSS for styling. The main configuration is in:

- `assets/css/tailwind.css` - Theme variables and base styles
- `tailwind.config.js` - Tailwind configuration

## 🔗 Enhanced Transaction Builder

### Overview

The Enhanced Transaction Builder is inspired by Mesh SDK patterns but uses custom interfaces and CardanoWASM directly. It provides a fluent API for building complex Cardano transactions without external dependencies.

### 🎯 Key Features

#### **1. Mesh SDK-Inspired Fluent API**
- **Method Chaining**: All methods return `this` for fluent API usage
- **Similar Method Names**: `txIn()`, `txOut()`, `txInDatumValue()`, `txInRedeemerValue()`, etc.
- **Enhanced Functionality**: Supports all major transaction building operations

#### **2. Comprehensive Transaction Support**
- **✅ Basic Transactions**: Send ADA and native tokens
- **✅ Smart Contract Interactions**: Plutus script support (V1, V2, V3)
- **✅ Minting/Burning**: Token minting with script support
- **✅ Staking Operations**: Certificate handling for stake registration/delegation
- **✅ Withdrawals**: Reward withdrawal support
- **✅ Metadata**: Transaction metadata handling
- **✅ Collateral**: Collateral input management

#### **3. Advanced Features**
- **Datum & Redeemer Support**: Inline datums and datum hashes
- **Script References**: Plutus and native script handling
- **Validity Ranges**: Time-based transaction validity
- **Required Signers**: Multi-signature support
- **Coin Selection**: Multiple strategies (LargestFirst, RandomImprove, etc.)

### 🔧 Interface Compatibility

#### **Custom Types Used:**
- `UTxO`, `TxOutput`, `Asset` from cardano types
- `Protocol` for protocol parameters
- `IFetcher`, `ISubmitter` for blockchain interaction
- `CardanoWASM` from utils

#### **Enhanced Types Added:**
```typescript
// Plutus script support
export type PlutusVersion = 'V1' | 'V2' | 'V3'
export type Datum = string | object
export type Redeemer = string | object

// Comprehensive transaction building
export interface MintAsset {
  assetName: string
  quantity: string
  policyId: string
  policyScript?: ScriptRef
  redeemer?: Redeemer
}

// Certificate and withdrawal support
export interface Certificate {
  type: CertificateType
  stakeKeyHash?: string
  poolKeyHash?: string
  // ... more fields
}
```

### 🚀 Usage Examples

#### **1. Basic Transaction (Mesh SDK Style)**
```typescript
import TxBuilder from '~/lib/hydra-wallet/transaction/tx-builder'

const txBuilder = new TxBuilder({ fetcher, submitter })
const tx = await txBuilder
  .txOut(recipientAddress, [{ unit: 'lovelace', quantity: '1000000' }])
  .selectUtxosFrom(utxos)
  .changeAddress(senderAddress)
  .complete()
```

#### **2. Smart Contract Interaction**
```typescript
const tx = await txBuilder
  .txIn(scriptUtxo.txHash, scriptUtxo.outputIndex, scriptUtxo.amount, scriptUtxo.address)
  .txInScript(scriptCbor)
  .txInDatumValue(datum)
  .txInRedeemerValue(redeemer)
  .spendingPlutusScript('V3')
  .txInCollateral(collateralTxHash, collateralIndex, collateralAmount, collateralAddress)
  .requiredSignerHash(signerPubKeyHash)
  .changeAddress(changeAddress)
  .complete()
```

#### **3. Token Minting**
```typescript
const tx = await txBuilder
  .mint('1000', policyId, assetName)
  .mintingScript(mintingScriptCbor)
  .mintRedeemerValue(mintRedeemer)
  .txOut(recipientAddress, [{ unit: policyId + assetName, quantity: '1000' }])
  .selectUtxosFrom(utxos)
  .changeAddress(senderAddress)
  .complete()
```

#### **4. Staking Operations**
```typescript
// Register stake address
const tx = await txBuilder
  .registerStake(rewardAddress)
  .selectUtxosFrom(utxos)
  .changeAddress(senderAddress)
  .complete()

// Delegate to pool
const tx = await txBuilder
  .delegateStake(rewardAddress, poolKeyHash)
  .selectUtxosFrom(utxos)
  .changeAddress(senderAddress)
  .complete()

// Withdraw rewards
const tx = await txBuilder
  .withdrawal(rewardAddress, rewardAmount)
  .selectUtxosFrom(utxos)
  .changeAddress(senderAddress)
  .complete()
```

#### **5. Complex Transaction with Metadata**
```typescript
const tx = await txBuilder
  .txOut(recipientAddress, [{ unit: 'lovelace', quantity: '2000000' }])
  .metadataValue('674', { msg: ['Hello', 'Cardano'] })
  .invalidAfter(currentSlot + 3600) // Valid for 1 hour
  .selectUtxosFrom(utxos)
  .changeAddress(senderAddress)
  .complete()
```

### 🔄 Backward Compatibility

All existing methods are preserved:
- `setInputs()` → now calls `selectUtxosFrom()`
- `addOutput()` → now calls `txOut()`
- `addLovelaceOutput()` → enhanced version
- `setChangeAddress()` → now calls `changeAddress()`
- `calculateFee()`, `setFee()`, `complete()` → enhanced versions

### 🎨 Architecture Highlights

#### **1. State Management**
- **Internal State Tracking**: Separate arrays for inputs, outputs, mints, certificates, etc.
- **Lazy Building**: Only builds CardanoWASM objects when `complete()` is called
- **Reset Capability**: `reset()` method to reuse builder instance

#### **2. Error Handling**
- **Validation**: Checks for proper method call order
- **Verbose Mode**: Optional logging for debugging
- **Graceful Degradation**: Handles missing CardanoWASM methods

#### **3. Extensibility**
- **Plugin Architecture**: Easy to add new transaction types
- **Script Support**: Comprehensive Plutus script handling
- **Future-Proof**: Designed to accommodate new Cardano features

### 🎯 Benefits Over Mesh SDK

1. **No External Dependencies**: Uses only CardanoWASM
2. **Custom Interface Compatibility**: Works with your existing types
3. **Enhanced Type Safety**: Full TypeScript support
4. **Hydra Optimized**: Built for layer 2 scaling
5. **Comprehensive Features**: Supports all major Cardano transaction types
6. **Better Performance**: Direct CardanoWASM usage without abstraction layers

## Learn More

- [Nuxt Documentation](https://nuxt.com/docs)
- [Shadcn Vue Documentation](https://www.shadcn-vue.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [CardanoWASM Documentation](https://docs.cardano.org/cardano-components/cardano-serialization-lib)
- [Cardano Developer Portal](https://developers.cardano.org/)

