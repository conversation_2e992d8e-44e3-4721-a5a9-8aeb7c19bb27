import LayoutImageHeaderAndBody from "~/components/layouts/image-header-and-body";
import { articleNew16 } from "~/data/links-articles";

export default function MDXPage({ children }) {
  return (
    <LayoutImageHeaderAndBody
      title={articleNew16.title}
      description={articleNew16.description}
      image={articleNew16.image}
      cover={articleNew16.cover}
    >
      {children}
    </LayoutImageHeaderAndBody>
  );
}

New article
