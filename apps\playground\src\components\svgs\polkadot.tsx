export default function SvgPolkadot({
  className,
  fill = "currentColor",
}: {
  className?: string;
  fill?: string;
}) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      viewBox="0 0 1326.1 1410.3"
      fill={fill}
      className={className}
    >
      <ellipse cx="663" cy="147.9" rx="254.3" ry="147.9" />
      <ellipse cx="663" cy="1262.3" rx="254.3" ry="147.9" />
      <ellipse
        transform="matrix(0.5 -0.866 0.866 0.5 -279.1512 369.5916)"
        cx="180.5"
        cy="426.5"
        rx="254.3"
        ry="148"
      />
      <ellipse
        transform="matrix(0.5 -0.866 0.866 0.5 -279.1552 1483.9517)"
        cx="1145.6"
        cy="983.7"
        rx="254.3"
        ry="147.9"
      />
      <ellipse
        transform="matrix(0.866 -0.5 0.5 0.866 -467.6798 222.044)"
        cx="180.5"
        cy="983.7"
        rx="148"
        ry="254.3"
      />
      <ellipse
        transform="matrix(0.866 -0.5 0.5 0.866 -59.8007 629.9254)"
        cx="1145.6"
        cy="426.6"
        rx="147.9"
        ry="254.3"
      />
    </svg>
  );
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="29"
      height="29"
      viewBox="0 0 29 29"
      fill={fill}
      className={className}
    >
      <path
        d="M20.9294 16.3976C20.9294 16.3976 20.9358 15.3266 20.3616 14.1253C19.0297 11.3398 14.9511 6.74717 2.44965 8.86911L0 13.0703L2.54633 15.0556C4.06401 14.4519 14.2443 8.67912 20.9294 16.3974V16.3976ZM4.08846 10.8233L3.24446 12.1819L1.88586 11.3379L4.08846 10.8233Z"
        fill="currentColor"
      ></path>
      <path
        d="M20.5776 20.3707C20.4668 19.713 19.3421 17.1372 16.296 15.7499C13.2916 14.3817 8.6545 14.2215 2.01156 18.1487L1.30078 22.5083L4.03963 23.745C4.03963 23.745 12.8396 14.251 20.5778 20.3704L20.5776 20.3707ZM3.72907 20.8007L2.16445 20.4564L4.07336 19.2358L3.72935 20.8004L3.72907 20.8007Z"
        fill="currentColor"
      ></path>
      <path
        d="M24.8502 4.43443C23.7434 3.24952 22.3305 2.38107 20.8536 1.74477L22.5703 0H12.2523L5.80469 2.91929L7.20152 5.91558C16.105 5.48023 22.8094 9.08584 23.1751 15.9556C23.5638 23.2615 16.3385 27.8964 9.38191 26.5819C7.07645 26.1462 5.89041 25.6401 5.89041 25.6401C7.46317 27.0198 10.67 28.7297 14.5114 28.7789C22.2549 28.8775 28.6281 22.5603 28.7751 14.6779C28.8495 10.6999 27.3408 7.08194 24.8496 4.43472L24.8502 4.43443ZM12.2526 3.01822L10.9311 1.69671H13.5741L12.2526 3.01822Z"
        fill="currentColor"
      ></path>
    </svg>
  );
}
