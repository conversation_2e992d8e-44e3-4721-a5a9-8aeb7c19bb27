> Thank you for contributing to <PERSON><PERSON>! We appreciate your effort and dedication to improving this project. To ensure that your contribution is in line with the project's guidelines and can be reviewed efficiently, please fill out the template below.

> Remember to follow our [Contributing Guide](CONTRIBUTING.md) before submitting your pull request.

## Summary

> Please provide a brief, concise summary of the changes in your pull request. Explain the problem you are trying to solve and the solution you have implemented.

## Affect components

> Please indicate which part of the Mesh Repo

- [ ] `@meshsdk/common`
- [ ] `@meshsdk/contract`
- [ ] `@meshsdk/core`
- [ ] `@meshsdk/core-csl`
- [ ] `@meshsdk/core-cst`
- [ ] `@meshsdk/hydra`
- [ ] `@meshsdk/provider`
- [ ] `@meshsdk/react`
- [ ] `@meshsdk/svelte`
- [ ] `@meshsdk/transaction`
- [ ] `@meshsdk/wallet`
- [ ] Mesh playground (i.e. <https://meshjs.dev/>)
- [ ] Mesh CLI

## Type of Change

> Please mark the relevant option(s) for your pull request:

- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] Code refactoring (improving code quality without changing its behavior)
- [ ] Documentation update (adding or updating documentation related to the project)

## Related Issues

> Please add the related issue here if any

## Checklist

> Please ensure that your pull request meets the following criteria:

- [ ] My code is appropriately commented and includes relevant documentation, if necessary
- [ ] I have added tests to cover my changes, if necessary
- [ ] I have updated the documentation, if necessary
- [ ] All new and existing tests pass (i.e. `npm run test`)
- [ ] The build is pass (i.e. `npm run build`)

## Additional Information

> If you have any additional information or context to provide, such as screenshots, relevant issues, or other details, please include them here.
