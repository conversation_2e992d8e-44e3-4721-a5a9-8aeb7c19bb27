# This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://docs.github.com/en/actions/publishing-packages/publishing-nodejs-packages

name: Lint, Test, and Build

on:
  release:
    types: [created]
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
      - name: Install dependencies
        run: npm install
      - name: Lint
        run: npm run lint
      - name: Build
        run: npm run build:mesh && npm run build:docs && npm run build:apps && npm run build:scripts
