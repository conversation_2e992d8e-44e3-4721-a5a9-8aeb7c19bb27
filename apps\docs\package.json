{"name": "docs", "version": "0.1.0", "private": true, "scripts": {"dev:docs": "next dev", "build:apps": "next build", "start": "next start"}, "dependencies": {"@algolia/autocomplete-core": "1.17.2", "@headlessui/react": "2.0.4", "@headlessui/tailwindcss": "0.2.1", "@sindresorhus/slugify": "2.2.1", "@tailwindcss/typography": "0.5.13", "clsx": "^2.1.1", "eslint-config-next": "^14.2.5", "flexsearch": "0.7.43", "framer-motion": "11.2.10", "next": "^14.2.5", "next-themes": "0.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-highlight-words": "0.20.0", "react-markdown": "9.0.1", "remark": "15.0.1", "simple-functional-loader": "1.2.1", "unist-util-filter": "5.0.1", "unist-util-visit": "5.0.0", "uuid": "^10.0.0", "zustand": "4.5.2"}, "devDependencies": {"@meshsdk/configs": "*", "@types/node": "20.14.2", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.19", "postcss": "8.4.38", "tailwindcss": "3.4.4", "typescript": "5.4.5"}}