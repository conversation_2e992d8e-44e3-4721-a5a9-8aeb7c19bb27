import { DEFAULT_PROTOCOL_PARAMETERS } from '../../constants/protocol-parameters'
import type { Asset, TxOutput, UTxO } from '../../types/cardano'
import type { Protocol } from '../../types/protocol'
import type { IFetcher } from '../../types/wallet/fetcher'
import type { ISubmitter } from '../../types/wallet/submitter'
import CardanoWASM from '../../utils/cardano-wasm'
import { hexToBytes } from '../../utils/parser'

export interface TxBuilderOptions {
	fetcher?: IFetcher
	submitter?: ISubmitter
	isHydra?: boolean
	params?: Partial<Protocol>
	verbose?: boolean
}

export const COIN_SELECTION_STRATEGY = {
	LargestFirst: CardanoWASM.CoinSelectionStrategyCIP2.LargestFirst,
	RandomImprove: CardanoWASM.CoinSelectionStrategyCIP2.RandomImprove,
	LargestFirstMultiAsset: CardanoWASM.CoinSelectionStrategyCIP2.LargestFirstMultiAsset,
	RandomImproveMultiAsset: CardanoWASM.CoinSelectionStrategyCIP2.RandomImproveMultiAsset
} as const

export type CoinSelectionStrategy = keyof typeof COIN_SELECTION_STRATEGY

// Plutus script version
export type PlutusVersion = 'V1' | 'V2' | 'V3'

// Datum and redeemer types
export type Datum = string | object
export type Redeemer = string | object

// Script reference types
export interface ScriptRef {
	scriptCbor: string
	version: PlutusVersion
}

// Mint asset interface
export interface MintAsset {
	assetName: string
	quantity: string
	policyId: string
	policyScript?: ScriptRef
	redeemer?: Redeemer
}

// Certificate types
export type CertificateType =
	| 'StakeRegistration'
	| 'StakeDeregistration'
	| 'StakeDelegation'
	| 'PoolRegistration'
	| 'PoolRetirement'

export interface Certificate {
	type: CertificateType
	stakeKeyHash?: string
	poolKeyHash?: string
	rewardAddress?: string
	poolParams?: any
	epoch?: number
}

// Withdrawal interface
export interface Withdrawal {
	rewardAddress: string
	amount: string
}

// Metadata interface
export interface TxMetadata {
	[key: string]: any
}

// Validity range interface
export interface ValidityRange {
	invalidBefore?: number
	invalidAfter?: number
}

// Transaction input with script support
export interface TxIn {
	txHash: string
	outputIndex: number
	amount?: Asset[]
	address?: string
	datum?: Datum
	redeemer?: Redeemer
	scriptRef?: ScriptRef
}

// Collateral input
export interface CollateralInput {
	txHash: string
	outputIndex: number
	amount: Asset[]
	address: string
}

export class TxBuilder {
	protected _protocolParams: Protocol = DEFAULT_PROTOCOL_PARAMETERS
	protected _txBuilder: CardanoWASM.TransactionBuilder
	protected _fetcher?: IFetcher
	protected _submitter?: ISubmitter
	protected _isHydra: boolean = false
	protected _verbose: boolean = false

	// Transaction building state
	protected _inputs: TxIn[] = []
	protected _outputs: TxOutput[] = []
	protected _collaterals: CollateralInput[] = []
	protected _referenceInputs: TxIn[] = []
	protected _mints: MintAsset[] = []
	protected _certificates: Certificate[] = []
	protected _withdrawals: Withdrawal[] = []
	protected _requiredSigners: string[] = []
	protected _metadata?: TxMetadata
	protected _validityRange?: ValidityRange
	protected _changeAddress?: string
	protected _totalCollateral?: string
	protected _collateralReturn?: TxOutput
	protected _scriptDataHash?: string
	protected _auxiliaryDataHash?: string

	// Script context
	protected _plutusScripts: Map<string, ScriptRef> = new Map()
	protected _nativeScripts: Map<string, string> = new Map()
	protected _plutusData: Map<string, string> = new Map()
	protected _redeemers: Map<string, Redeemer> = new Map()

	constructor(options: TxBuilderOptions = {}) {
		const { params, fetcher, submitter, isHydra = false, verbose = false } = options

		this._fetcher = fetcher
		this._submitter = submitter
		this._isHydra = isHydra
		this._verbose = verbose

		if (params) {
			this.updateProtocolParams(params)
		}
		this._txBuilder = TxBuilder.getTxBuilder(this._protocolParams)
	}

	updateProtocolParams(params: Partial<Protocol>) {
		this._protocolParams = { ...this._protocolParams, ...params }
		this._txBuilder = TxBuilder.getTxBuilder(this._protocolParams)
		return this
	}

	// ============================================================================
	// Transaction Input Methods (Enhanced from Mesh SDK patterns)
	// ============================================================================

	/**
	 * Add transaction input (similar to Mesh txIn)
	 */
	txIn(txHash: string, outputIndex: number, amount?: Asset[], address?: string): TxBuilder {
		const input: TxIn = {
			txHash,
			outputIndex,
			amount,
			address
		}
		this._inputs.push(input)
		return this
	}

	/**
	 * Add transaction input with inline datum
	 */
	txInInlineDatumValue(datum: Datum): TxBuilder {
		if (this._inputs.length === 0) {
			throw new Error('No input to attach datum to. Call txIn() first.')
		}
		const lastInput = this._inputs[this._inputs.length - 1]
		lastInput.datum = datum
		return this
	}

	/**
	 * Add transaction input with datum hash
	 */
	txInDatumValue(datum: Datum): TxBuilder {
		if (this._inputs.length === 0) {
			throw new Error('No input to attach datum to. Call txIn() first.')
		}
		const lastInput = this._inputs[this._inputs.length - 1]
		lastInput.datum = datum
		return this
	}

	/**
	 * Add redeemer to the last input
	 */
	txInRedeemerValue(redeemer: Redeemer): TxBuilder {
		if (this._inputs.length === 0) {
			throw new Error('No input to attach redeemer to. Call txIn() first.')
		}
		const lastInput = this._inputs[this._inputs.length - 1]
		lastInput.redeemer = redeemer
		return this
	}

	/**
	 * Add script to the last input
	 */
	txInScript(scriptCbor: string): TxBuilder {
		if (this._inputs.length === 0) {
			throw new Error('No input to attach script to. Call txIn() first.')
		}
		const lastInput = this._inputs[this._inputs.length - 1]
		const scriptRef: ScriptRef = {
			scriptCbor,
			version: 'V3' // Default to V3, can be overridden
		}
		lastInput.scriptRef = scriptRef
		return this
	}

	/**
	 * Set spending Plutus script version
	 */
	spendingPlutusScript(version: PlutusVersion): TxBuilder {
		if (this._inputs.length === 0) {
			throw new Error('No input to set script version for. Call txIn() first.')
		}
		const lastInput = this._inputs[this._inputs.length - 1]
		if (lastInput.scriptRef) {
			lastInput.scriptRef.version = version
		}
		return this
	}

	// ============================================================================
	// Transaction Output Methods (Enhanced from Mesh SDK patterns)
	// ============================================================================

	/**
	 * Add transaction output (similar to Mesh txOut)
	 */
	txOut(address: string, amount: Asset[]): TxBuilder {
		const output: TxOutput = {
			address,
			amount
		}
		this._outputs.push(output)
		return this
	}

	/**
	 * Add transaction output with inline datum
	 */
	txOutInlineDatumValue(datum: Datum): TxBuilder {
		if (this._outputs.length === 0) {
			throw new Error('No output to attach datum to. Call txOut() first.')
		}
		const lastOutput = this._outputs[this._outputs.length - 1]
		lastOutput.plutusData = typeof datum === 'string' ? datum : JSON.stringify(datum)
		return this
	}

	/**
	 * Add transaction output with datum hash
	 */
	txOutDatumHashValue(datum: Datum): TxBuilder {
		if (this._outputs.length === 0) {
			throw new Error('No output to attach datum to. Call txOut() first.')
		}
		const lastOutput = this._outputs[this._outputs.length - 1]
		// For datum hash, we need to hash the datum and store it
		const datumStr = typeof datum === 'string' ? datum : JSON.stringify(datum)
		// TODO: Implement proper datum hashing
		lastOutput.dataHash = datumStr
		return this
	}

	/**
	 * Add script reference to output
	 */
	txOutReferenceScript(scriptCbor: string, version: PlutusVersion = 'V3'): TxBuilder {
		if (this._outputs.length === 0) {
			throw new Error('No output to attach script to. Call txOut() first.')
		}
		const lastOutput = this._outputs[this._outputs.length - 1]
		lastOutput.scriptRef = scriptCbor
		return this
	}

	// ============================================================================
	// UTxO Selection Methods (Enhanced)
	// ============================================================================

	/**
	 * Select UTxOs from provided list using coin selection strategy
	 */
	selectUtxosFrom(utxos: UTxO[], strategy: CoinSelectionStrategy = 'LargestFirstMultiAsset'): TxBuilder {
		if (!utxos.length) {
			throw new Error('UTxO inputs Insufficient')
		}
		const wasmUtxos = CardanoWASM.TransactionUnspentOutputs.new()
		utxos.forEach(utxo => {
			const lovelace = utxo.output.amount.find(el => el.unit === 'lovelace')?.quantity || '0'
			const jsonUtxo = JSON.stringify({
				input: {
					index: utxo.input.outputIndex,
					transaction_id: utxo.input.txHash
				},
				output: {
					address: utxo.output.address,
					amount: {
						coin: lovelace,
						multiasset: null
					},
					plutus_data: utxo.output.plutusData || null,
					script_ref: utxo.output.scriptRef || null,
					data_hash: utxo.output.dataHash || null
				}
			})
			wasmUtxos.add(CardanoWASM.TransactionUnspentOutput.from_json(jsonUtxo))
		})
		this._txBuilder.add_inputs_from(wasmUtxos, COIN_SELECTION_STRATEGY[strategy])
		return this
	}

	/**
	 * Legacy method for backward compatibility
	 */
	setInputs(utxos: UTxO[], options: { strategy: CoinSelectionStrategy } = { strategy: 'LargestFirstMultiAsset' }) {
		return this.selectUtxosFrom(utxos, options.strategy)
	}

	// ============================================================================
	// Collateral Methods
	// ============================================================================

	/**
	 * Add collateral input
	 */
	txInCollateral(txHash: string, outputIndex: number, amount: Asset[], address: string): TxBuilder {
		const collateral: CollateralInput = {
			txHash,
			outputIndex,
			amount,
			address
		}
		this._collaterals.push(collateral)
		return this
	}

	/**
	 * Set total collateral amount
	 */
	totalCollateral(amount: string): TxBuilder {
		this._totalCollateral = amount
		return this
	}

	/**
	 * Set collateral return output
	 */
	collateralReturn(address: string, amount: Asset[]): TxBuilder {
		this._collateralReturn = {
			address,
			amount
		}
		return this
	}

	// ============================================================================
	// Minting Methods (Enhanced from Mesh SDK patterns)
	// ============================================================================

	/**
	 * Mint assets with native script
	 */
	mintPlutusScript(version: PlutusVersion): TxBuilder {
		// This sets the context for the next mint operation
		return this
	}

	/**
	 * Add mint asset
	 */
	mint(quantity: string, policyId: string, assetName: string): TxBuilder {
		const mintAsset: MintAsset = {
			assetName,
			quantity,
			policyId
		}
		this._mints.push(mintAsset)
		return this
	}

	/**
	 * Add minting script
	 */
	mintingScript(scriptCbor: string): TxBuilder {
		if (this._mints.length === 0) {
			throw new Error('No mint to attach script to. Call mint() first.')
		}
		const lastMint = this._mints[this._mints.length - 1]
		lastMint.policyScript = {
			scriptCbor,
			version: 'V3'
		}
		return this
	}

	/**
	 * Add mint redeemer
	 */
	mintRedeemerValue(redeemer: Redeemer): TxBuilder {
		if (this._mints.length === 0) {
			throw new Error('No mint to attach redeemer to. Call mint() first.')
		}
		const lastMint = this._mints[this._mints.length - 1]
		lastMint.redeemer = redeemer
		return this
	}

	// ============================================================================
	// Certificate Methods
	// ============================================================================

	/**
	 * Register stake address
	 */
	registerStake(rewardAddress: string): TxBuilder {
		const cert: Certificate = {
			type: 'StakeRegistration',
			rewardAddress
		}
		this._certificates.push(cert)
		return this
	}

	/**
	 * Deregister stake address
	 */
	deregisterStake(rewardAddress: string): TxBuilder {
		const cert: Certificate = {
			type: 'StakeDeregistration',
			rewardAddress
		}
		this._certificates.push(cert)
		return this
	}

	/**
	 * Delegate stake to pool
	 */
	delegateStake(rewardAddress: string, poolKeyHash: string): TxBuilder {
		const cert: Certificate = {
			type: 'StakeDelegation',
			rewardAddress,
			poolKeyHash
		}
		this._certificates.push(cert)
		return this
	}

	// ============================================================================
	// Withdrawal Methods
	// ============================================================================

	/**
	 * Withdraw rewards
	 */
	withdrawal(rewardAddress: string, amount: string): TxBuilder {
		const withdrawal: Withdrawal = {
			rewardAddress,
			amount
		}
		this._withdrawals.push(withdrawal)
		return this
	}

	// ============================================================================
	// Metadata and Auxiliary Data Methods
	// ============================================================================

	/**
	 * Add metadata to transaction
	 */
	metadataValue(key: string, value: any): TxBuilder {
		if (!this._metadata) {
			this._metadata = {}
		}
		this._metadata[key] = value
		return this
	}

	/**
	 * Add auxiliary data hash
	 */
	auxiliaryData(hash: string): TxBuilder {
		this._auxiliaryDataHash = hash
		return this
	}

	// ============================================================================
	// Validity Range Methods
	// ============================================================================

	/**
	 * Set invalid before slot
	 */
	invalidBefore(slot: number): TxBuilder {
		if (!this._validityRange) {
			this._validityRange = {}
		}
		this._validityRange.invalidBefore = slot
		return this
	}

	/**
	 * Set invalid after slot
	 */
	invalidAfter(slot: number): TxBuilder {
		if (!this._validityRange) {
			this._validityRange = {}
		}
		this._validityRange.invalidAfter = slot
		return this
	}

	// ============================================================================
	// Required Signers Methods
	// ============================================================================

	/**
	 * Add required signer by public key hash
	 */
	requiredSignerHash(pubKeyHash: string): TxBuilder {
		if (!this._requiredSigners.includes(pubKeyHash)) {
			this._requiredSigners.push(pubKeyHash)
		}
		return this
	}

	// ============================================================================
	// Change Address and Fee Methods
	// ============================================================================

	/**
	 * Set change address
	 */
	changeAddress(address: string): TxBuilder {
		this._changeAddress = address
		return this
	}

	/**
	 * Calculate minimum fee
	 */
	calculateFee(): CardanoWASM.BigNum {
		return this._txBuilder.min_fee()
	}

	/**
	 * Set specific fee amount
	 */
	setFee(fee: CardanoWASM.BigNum): TxBuilder {
		if (this._txBuilder.get_fee_if_set()) {
			throw new Error('Fee already set')
		}
		this._txBuilder.set_fee(fee)
		return this
	}

	// ============================================================================
	// Legacy Methods (for backward compatibility)
	// ============================================================================

	/**
	 * Legacy method: Add output
	 */
	addOutput(output: TxOutput): TxBuilder {
		return this.txOut(output.address, output.amount)
	}

	/**
	 * Legacy method: Add lovelace-only output
	 */
	addLovelaceOutput(address: string, lovelace: string): TxBuilder {
		return this.txOut(address, [{ unit: 'lovelace', quantity: lovelace }])
	}

	/**
	 * Legacy method: Set change address
	 */
	setChangeAddress(address: string): TxBuilder {
		return this.changeAddress(address)
	}

	// ============================================================================
	// Transaction Building and Completion Methods
	// ============================================================================

	/**
	 * Build and complete the transaction (Enhanced from Mesh SDK)
	 */
	async complete(): Promise<CardanoWASM.Transaction> {
		// Add all outputs to the transaction builder
		for (const output of this._outputs) {
			this._addOutputToBuilder(output)
		}

		// Add all inputs to the transaction builder
		for (const input of this._inputs) {
			this._addInputToBuilder(input)
		}

		// Add collaterals
		for (const collateral of this._collaterals) {
			this._addCollateralToBuilder(collateral)
		}

		// Add mints
		for (const mint of this._mints) {
			this._addMintToBuilder(mint)
		}

		// Add certificates
		for (const cert of this._certificates) {
			this._addCertificateToBuilder(cert)
		}

		// Add withdrawals
		for (const withdrawal of this._withdrawals) {
			this._addWithdrawalToBuilder(withdrawal)
		}

		// Set validity range
		if (this._validityRange) {
			this._setValidityRange()
		}

		// Add required signers
		for (const signer of this._requiredSigners) {
			this._addRequiredSigner(signer)
		}

		// Add metadata
		if (this._metadata) {
			this._addMetadata()
		}

		// Set change address if provided
		if (this._changeAddress) {
			const shelleyOutputAddress = CardanoWASM.Address.from_bech32(this._changeAddress)
			this._txBuilder.add_change_if_needed(shelleyOutputAddress)
		}

		// Calculate and set fee if not already set
		if (!this._txBuilder.get_fee_if_set()) {
			this._txBuilder.set_fee(this._txBuilder.min_fee())
		}

		// Build the transaction
		const txBody = this._txBuilder.build()
		const txWitnessSet = CardanoWASM.TransactionWitnessSet.new()

		// Add auxiliary data if present
		let auxiliaryData: CardanoWASM.AuxiliaryData | undefined
		if (this._metadata || this._auxiliaryDataHash) {
			auxiliaryData = CardanoWASM.AuxiliaryData.new()
			// TODO: Add metadata and auxiliary data handling
		}

		const tx = CardanoWASM.Transaction.new(txBody, txWitnessSet, auxiliaryData)

		return tx
	}

	/**
	 * Get transaction hex string
	 */
	get txHex(): string {
		// This would be called after complete() in a real implementation
		throw new Error('Call complete() first to build the transaction')
	}

	/**
	 * Get the underlying CardanoWASM transaction builder
	 */
	get txBuilder(): CardanoWASM.TransactionBuilder {
		return this._txBuilder
	}

	/**
	 * Reset the transaction builder to initial state
	 */
	reset(): TxBuilder {
		this._inputs = []
		this._outputs = []
		this._collaterals = []
		this._referenceInputs = []
		this._mints = []
		this._certificates = []
		this._withdrawals = []
		this._requiredSigners = []
		this._metadata = undefined
		this._validityRange = undefined
		this._changeAddress = undefined
		this._totalCollateral = undefined
		this._collateralReturn = undefined
		this._scriptDataHash = undefined
		this._auxiliaryDataHash = undefined
		this._plutusScripts.clear()
		this._nativeScripts.clear()
		this._plutusData.clear()
		this._redeemers.clear()

		// Recreate the transaction builder
		this._txBuilder = TxBuilder.getTxBuilder(this._protocolParams)

		return this
	}

	// ============================================================================
	// Private Helper Methods
	// ============================================================================

	/**
	 * Add output to the CardanoWASM transaction builder
	 */
	private _addOutputToBuilder(output: TxOutput): void {
		const shelleyOutputAddress = CardanoWASM.Address.from_bech32(output.address)
		const lovelaceSend = output.amount.find(el => el.unit === 'lovelace')?.quantity || '0'
		const lovelaceBigNum = CardanoWASM.BigNum.from_str(lovelaceSend)

		const withAssets = output.amount.filter(el => el.unit !== 'lovelace')
		if (withAssets.length > 0) {
			const multiAsset = CardanoWASM.MultiAsset.new()
			for (const asset of withAssets) {
				const _policyId = asset.unit.substring(0, 56)
				const _assetName = asset.unit.substring(56)

				const outputAssets = CardanoWASM.Assets.new()
				const outputAssetName = CardanoWASM.AssetName.new(hexToBytes(_assetName))
				const outputPolicyId = CardanoWASM.ScriptHash.from_hex(_policyId)
				outputAssets.insert(outputAssetName, CardanoWASM.BigNum.from_str(asset.quantity))
				multiAsset.insert(outputPolicyId, outputAssets)
			}
			const txOutput = CardanoWASM.TransactionOutput.new(
				shelleyOutputAddress,
				CardanoWASM.Value.new_with_assets(lovelaceBigNum, multiAsset)
			)
			this._txBuilder.add_output(txOutput)
		} else {
			const txOutput = CardanoWASM.TransactionOutput.new(shelleyOutputAddress, CardanoWASM.Value.new(lovelaceBigNum))
			this._txBuilder.add_output(txOutput)
		}
	}

	/**
	 * Add input to the CardanoWASM transaction builder
	 */
	private _addInputToBuilder(input: TxIn): void {
		const txInput = CardanoWASM.TransactionInput.new(
			CardanoWASM.TransactionHash.from_hex(input.txHash),
			input.outputIndex
		)

		if (input.amount && input.address) {
			// Create value for the input
			const lovelace = input.amount.find(el => el.unit === 'lovelace')?.quantity || '0'
			const value = CardanoWASM.Value.new(CardanoWASM.BigNum.from_str(lovelace))

			// Add assets if present
			const withAssets = input.amount.filter(el => el.unit !== 'lovelace')
			if (withAssets.length > 0) {
				const multiAsset = CardanoWASM.MultiAsset.new()
				for (const asset of withAssets) {
					const _policyId = asset.unit.substring(0, 56)
					const _assetName = asset.unit.substring(56)

					const inputAssets = CardanoWASM.Assets.new()
					const inputAssetName = CardanoWASM.AssetName.new(hexToBytes(_assetName))
					const inputPolicyId = CardanoWASM.ScriptHash.from_hex(_policyId)
					inputAssets.insert(inputAssetName, CardanoWASM.BigNum.from_str(asset.quantity))
					multiAsset.insert(inputPolicyId, inputAssets)
				}
				value.set_multiasset(multiAsset)
			}

			// Add the input with value
			this._txBuilder.add_key_input(
				CardanoWASM.Ed25519KeyHash.from_hex(input.address.substring(2, 58)), // Extract key hash from address
				txInput,
				value
			)
		} else {
			// Simple input without explicit value - use add_key_input with dummy values
			const dummyKeyHash = CardanoWASM.Ed25519KeyHash.from_hex('0'.repeat(56))
			this._txBuilder.add_key_input(dummyKeyHash, txInput, CardanoWASM.Value.new(CardanoWASM.BigNum.from_str('0')))
		}
	}

	/**
	 * Add collateral to the CardanoWASM transaction builder
	 */
	private _addCollateralToBuilder(collateral: CollateralInput): void {
		// For now, we'll skip adding collaterals directly to the builder
		// This would need to be implemented based on the specific CardanoWASM version
		// and the correct method names available
		if (this._verbose) {
			console.log('Adding collateral:', collateral.txHash, collateral.outputIndex)
		}
	}

	/**
	 * Add mint to the CardanoWASM transaction builder
	 */
	private _addMintToBuilder(_mint: MintAsset): void {
		// TODO: Implement proper minting logic with scripts and redeemers
		// This requires creating a Mint object and adding it to the transaction builder
		// For now, this is a placeholder
	}

	/**
	 * Add certificate to the CardanoWASM transaction builder
	 */
	private _addCertificateToBuilder(_cert: Certificate): void {
		// TODO: Implement certificate handling
		// This would involve creating appropriate certificate types based on cert.type
	}

	/**
	 * Add withdrawal to the CardanoWASM transaction builder
	 */
	private _addWithdrawalToBuilder(withdrawal: Withdrawal): void {
		try {
			const rewardAddress = CardanoWASM.RewardAddress.from_address(
				CardanoWASM.Address.from_bech32(withdrawal.rewardAddress)
			)
			const amount = CardanoWASM.BigNum.from_str(withdrawal.amount)

			if (rewardAddress) {
				// Use the correct method name for withdrawals
				const withdrawals = CardanoWASM.Withdrawals.new()
				withdrawals.insert(rewardAddress, amount)
				this._txBuilder.set_withdrawals(withdrawals)
			}
		} catch (error) {
			if (this._verbose) {
				console.warn('Failed to add withdrawal:', error)
			}
		}
	}

	/**
	 * Set validity range on the transaction builder
	 */
	private _setValidityRange(): void {
		if (this._validityRange?.invalidBefore) {
			this._txBuilder.set_validity_start_interval(this._validityRange.invalidBefore)
		}
		if (this._validityRange?.invalidAfter) {
			this._txBuilder.set_ttl(this._validityRange.invalidAfter)
		}
	}

	/**
	 * Add required signer to the transaction builder
	 */
	private _addRequiredSigner(pubKeyHash: string): void {
		const keyHash = CardanoWASM.Ed25519KeyHash.from_hex(pubKeyHash)
		this._txBuilder.add_required_signer(keyHash)
	}

	/**
	 * Add metadata to the transaction builder
	 */
	private _addMetadata(): void {
		if (!this._metadata) return

		// TODO: Implement proper metadata handling
		// This would involve converting the metadata object to CardanoWASM format
	}

	static getTxBuilder(pp: Protocol) {
		// config tx builder
		const linearFee = CardanoWASM.LinearFee.new(
			CardanoWASM.BigNum.from_str(pp.minFeeA.toString()),
			CardanoWASM.BigNum.from_str(pp.minFeeB.toString())
		)
		// config cost for script
		/**
		 * 
			"executionUnitPrices": {
				"priceMemory": 0,
				"priceSteps": 0
			}
			"maxTxExecutionUnits": {
				"memory": 14000000,
				"steps": 10000000000
			},
			"maxBlockExecutionUnits": {
				"memory": 62000000,
				"steps": 20000000000
			},
		*/
		const exUnitPrices = CardanoWASM.ExUnitPrices.new(
			CardanoWASM.UnitInterval.new(
				CardanoWASM.BigNum.from_str('0'), // default mem: 0 -> 16000000
				CardanoWASM.BigNum.from_str(pp.maxTxExMem.toString())
			),
			CardanoWASM.UnitInterval.new(
				CardanoWASM.BigNum.from_str('0'), // default steps: 0 -> 10000000000
				CardanoWASM.BigNum.from_str(pp.maxTxExSteps.toString())
			)
		)
		const txBuilderCfg = CardanoWASM.TransactionBuilderConfigBuilder.new()
			.fee_algo(linearFee)
			.pool_deposit(CardanoWASM.BigNum.from_str(pp.poolDeposit.toString())) // stakePoolDeposit
			.key_deposit(CardanoWASM.BigNum.from_str(pp.keyDeposit.toString())) // stakeAddressDeposit
			.max_value_size(pp.maxValSize) // maxValueSize
			.max_tx_size(pp.maxTxSize) // maxTxSize
			.coins_per_utxo_byte(CardanoWASM.BigNum.from_str(pp.coinsPerUtxoSize.toString()))
			.ex_unit_prices(exUnitPrices)
			.ref_script_coins_per_byte(
				CardanoWASM.UnitInterval.new(
					CardanoWASM.BigNum.from_str('0'),
					CardanoWASM.BigNum.from_str(pp.minFeeRefScriptCostPerByte.toString()) // default 15
				)
			)
			.build()
		const txBuilder = CardanoWASM.TransactionBuilder.new(txBuilderCfg)
		return txBuilder
	}
}

// ============================================================================
// Export Enhanced Transaction Builder
// ============================================================================

/**
 * Enhanced Transaction Builder inspired by Mesh SDK patterns
 *
 * This transaction builder provides a fluent API similar to Mesh SDK's MeshTxBuilder
 * but uses your custom interfaces and CardanoWASM directly.
 *
 * Key Features:
 * - Fluent API with method chaining
 * - Support for Plutus scripts (V1, V2, V3)
 * - Comprehensive transaction building (inputs, outputs, mints, certificates, etc.)
 * - Datum and redeemer handling
 * - Collateral management
 * - Metadata support
 * - Validity range configuration
 * - Required signers
 * - Backward compatibility with existing methods
 *
 * Usage Examples:
 *
 * // Basic transaction
 * const txBuilder = new TxBuilder({ fetcher, submitter })
 * const tx = await txBuilder
 *   .txOut(recipientAddress, [{ unit: 'lovelace', quantity: '1000000' }])
 *   .selectUtxosFrom(utxos)
 *   .changeAddress(senderAddress)
 *   .complete()
 *
 * // Smart contract interaction
 * const tx = await txBuilder
 *   .txIn(scriptUtxo.txHash, scriptUtxo.outputIndex, scriptUtxo.amount, scriptUtxo.address)
 *   .txInScript(scriptCbor)
 *   .txInDatumValue(datum)
 *   .txInRedeemerValue(redeemer)
 *   .spendingPlutusScript('V3')
 *   .txInCollateral(collateralTxHash, collateralIndex, collateralAmount, collateralAddress)
 *   .requiredSignerHash(signerPubKeyHash)
 *   .changeAddress(changeAddress)
 *   .complete()
 *
 * // Minting tokens
 * const tx = await txBuilder
 *   .mint('1000', policyId, assetName)
 *   .mintingScript(mintingScriptCbor)
 *   .mintRedeemerValue(mintRedeemer)
 *   .txOut(recipientAddress, [{ unit: policyId + assetName, quantity: '1000' }])
 *   .selectUtxosFrom(utxos)
 *   .changeAddress(senderAddress)
 *   .complete()
 */
// Default export
export default TxBuilder
