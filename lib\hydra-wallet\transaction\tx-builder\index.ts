import { DEFAULT_PROTOCOL_PARAMETERS } from '../../constants/protocol-parameters'
import type { TxOutput, UTxO } from '../../types/cardano'
import type { Protocol } from '../../types/protocol'
import type { IFetcher } from '../../types/wallet/fetcher'
import type { ISubmitter } from '../../types/wallet/submitter'
import CardanoWASM from '../../utils/cardano-wasm'
import { hexToBytes } from '../../utils/parser'

export interface TxBuilderOptions {
	fetcher?: IFetcher
	submitter?: ISubmitter
	isHydra?: boolean
	params?: Partial<Protocol>
	verbose?: boolean
}

export const COIN_SELECTION_STRATEGY = {
	LargestFirst: CardanoWASM.CoinSelectionStrategyCIP2.LargestFirst,
	RandomImprove: CardanoWASM.CoinSelectionStrategyCIP2.RandomImprove,
	LargestFirstMultiAsset: CardanoWASM.CoinSelectionStrategyCIP2.LargestFirstMultiAsset,
	RandomImproveMultiAsset: CardanoWASM.CoinSelectionStrategyCIP2.RandomImproveMultiAsset
} as const

export class TxBuilder {
	protected _protocolParams: Protocol = DEFAULT_PROTOCOL_PARAMETERS
	protected _txBuilder: CardanoWASM.TransactionBuilder
	protected _txBuilderBody: {
		inputs: CardanoWASM.TransactionInput[]
		outputs: CardanoWASM.TransactionOutput[]
		fee: CardanoWASM.BigNum
		collaterals: CardanoWASM.TransactionInput[]
		requiredSignatures: string[]
		referenceInputs: CardanoWASM.TransactionInput[]
		mints: CardanoWASM.Mint[]
		changeAddress: string
		metadata: CardanoWASM.AuxiliaryData
	}

	constructor(options: TxBuilderOptions = {}) {
		const { params } = options
		if (params) {
			this.updateProtocolParams(params)
		}
		this._txBuilder = TxBuilder.getTxBuilder(this._protocolParams)
	}

	updateProtocolParams(params: Partial<Protocol>) {
		this._protocolParams = { ...this._protocolParams, ...params }
		return this
	}

	setInputs(
		utxos: UTxO[],
		options: { strategy: keyof typeof COIN_SELECTION_STRATEGY } = { strategy: 'LargestFirstMultiAsset' }
	) {
		if (!utxos.length) {
			throw new Error('UTxO inputs Insufficient')
		}
		const wasmUtxos = CardanoWASM.TransactionUnspentOutputs.new()
		utxos.forEach(utxo => {
			const lovelace = utxo.output.amount.find(el => el.unit === 'lovelace')?.quantity || '0'
			const jsonUtxo = JSON.stringify({
				input: {
					index: utxo.input.outputIndex,
					transaction_id: utxo.input.txHash
				},
				output: {
					address: utxo.output.address,
					amount: {
						coin: lovelace,
						multiasset: null
					},
					// TODO: update it
					plutus_data: utxo.output.plutusData || null,
					script_ref: utxo.output.scriptRef || null,
					data_hash: utxo.output.dataHash || null
				}
			})
			wasmUtxos.add(CardanoWASM.TransactionUnspentOutput.from_json(jsonUtxo))
		})
		this._txBuilder.add_inputs_from(wasmUtxos, COIN_SELECTION_STRATEGY[options.strategy])
		return this
	}

	addOutput(output: TxOutput) {
		const shelleyOutputAddress = CardanoWASM.Address.from_bech32(output.address)
		const lovelaceSend = output.amount.find(el => el.unit === 'lovelace')?.quantity || '0'
		const lovelaceBigNum = CardanoWASM.BigNum.from_str(lovelaceSend)

		const withAssets = output.amount.filter(el => el.unit !== 'lovelace')
		if (withAssets.length > 0) {
			const multiAsset = CardanoWASM.MultiAsset.new()
			for (const asset of withAssets) {
				const _policyId = asset.unit.substring(0, 56)
				const _assetName = asset.unit.substring(56)

				const outputAssets = CardanoWASM.Assets.new()
				const outputAssetName = CardanoWASM.AssetName.new(hexToBytes(_assetName))
				const outputPolicyId = CardanoWASM.ScriptHash.from_hex(_policyId)
				outputAssets.insert(outputAssetName, CardanoWASM.BigNum.from_str(asset.quantity))
				multiAsset.insert(outputPolicyId, outputAssets)
			}
			const txOutput = CardanoWASM.TransactionOutput.new(
				shelleyOutputAddress,
				CardanoWASM.Value.new_with_assets(lovelaceBigNum, multiAsset)
			)
			this._txBuilder.add_output(txOutput)
		} else {
			const txOutput = CardanoWASM.TransactionOutput.new(shelleyOutputAddress, CardanoWASM.Value.new(lovelaceBigNum))
			this._txBuilder.add_output(txOutput)
		}
		return this
	}

	addLovelaceOutput(address: string, lovelace: string) {
		const shelleyOutputAddress = CardanoWASM.Address.from_bech32(address)
		const txOutput = CardanoWASM.TransactionOutput.new(
			shelleyOutputAddress,
			CardanoWASM.Value.new(CardanoWASM.BigNum.from_str(lovelace))
		)
		this._txBuilder.add_output(txOutput)
		return this
	}

	setChangeAddress(address: string) {
		const shelleyOutputAddress = CardanoWASM.Address.from_bech32(address)
		this._txBuilder.add_change_if_needed(shelleyOutputAddress)
		return this
	}

	calculateFee() {
		return this._txBuilder.min_fee()
	}

	setFee(fee: CardanoWASM.BigNum) {
		if (this._txBuilder.get_fee_if_set()) {
			throw new Error('Fee already set')
		}
		this._txBuilder.set_fee(fee)
		return this
	}

	complete() {
		if (!this._txBuilder.get_fee_if_set()) {
			this._txBuilder.set_fee(this._txBuilder.min_fee())
		}
		const txBody = this._txBuilder.build()
		const txWitnessSet = CardanoWASM.TransactionWitnessSet.new()
		const tx = CardanoWASM.Transaction.new(
			txBody,
			txWitnessSet
			// auxiliaryData // Auxiliary data
		)
		return tx
	}

	get txBuilder() {
		return this._txBuilder
	}

	static getTxBuilder(pp: Protocol) {
		// config tx builder
		const linearFee = CardanoWASM.LinearFee.new(
			CardanoWASM.BigNum.from_str(pp.minFeeA.toString()),
			CardanoWASM.BigNum.from_str(pp.minFeeB.toString())
		)
		// config cost for script
		/**
		 * 
			"executionUnitPrices": {
				"priceMemory": 0,
				"priceSteps": 0
			}
			"maxTxExecutionUnits": {
				"memory": 14000000,
				"steps": 10000000000
			},
			"maxBlockExecutionUnits": {
				"memory": 62000000,
				"steps": 20000000000
			},
		*/
		const exUnitPrices = CardanoWASM.ExUnitPrices.new(
			CardanoWASM.UnitInterval.new(
				CardanoWASM.BigNum.from_str('0'), // default mem: 0 -> 16000000
				CardanoWASM.BigNum.from_str(pp.maxTxExMem.toString())
			),
			CardanoWASM.UnitInterval.new(
				CardanoWASM.BigNum.from_str('0'), // default steps: 0 -> 10000000000
				CardanoWASM.BigNum.from_str(pp.maxTxExSteps.toString())
			)
		)
		const txBuilderCfg = CardanoWASM.TransactionBuilderConfigBuilder.new()
			.fee_algo(linearFee)
			.pool_deposit(CardanoWASM.BigNum.from_str(pp.poolDeposit.toString())) // stakePoolDeposit
			.key_deposit(CardanoWASM.BigNum.from_str(pp.keyDeposit.toString())) // stakeAddressDeposit
			.max_value_size(pp.maxValSize) // maxValueSize
			.max_tx_size(pp.maxTxSize) // maxTxSize
			.coins_per_utxo_byte(CardanoWASM.BigNum.from_str(pp.coinsPerUtxoSize.toString()))
			.ex_unit_prices(exUnitPrices)
			.ref_script_coins_per_byte(
				CardanoWASM.UnitInterval.new(
					CardanoWASM.BigNum.from_str('0'),
					CardanoWASM.BigNum.from_str(pp.minFeeRefScriptCostPerByte.toString()) // default 15
				)
			)
			.build()
		const txBuilder = CardanoWASM.TransactionBuilder.new(txBuilderCfg)
		return txBuilder
	}
}
